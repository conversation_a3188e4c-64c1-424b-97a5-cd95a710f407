<template>
  <div class="task-detail">
    <div class="page-header">
      <el-button @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <div class="header-info">
        <h1>任务详情</h1>
        <p v-if="order">工单 #{{ order.orderId }}</p>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="order" class="task-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(order.status)" size="large">
              {{ getStatusText(order.status) }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="isMobile ? 1 : 2" border>
          <el-descriptions-item label="工单号">
            #{{ order.orderId }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(order.status)" size="small">
              {{ getStatusText(order.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="车辆">
            {{ order.vehicle?.licensePlate }} {{ order.vehicle?.brand }} {{ order.vehicle?.model }}
          </el-descriptions-item>
          <el-descriptions-item label="车主">
            {{ order.user?.realName }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ order.user?.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="故障类型">
            {{ order.faultType?.typeName }}
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag :type="getUrgencyType(order.urgencyLevel)" size="small">
              {{ getUrgencyText(order.urgencyLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="预计工时">
            {{ order.faultType?.estimatedHours }}小时
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ formatDate(order.submitTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="预计完成时间">
            {{ formatDate(order.estimatedCompletionTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="故障描述" :span="2">
            {{ order.description }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 工作进度卡片 -->
      <el-card class="progress-card">
        <template #header>
          <span>工作进度</span>
        </template>

        <el-steps :active="getStepActive(order.status)" finish-status="success">
          <el-step title="工单分配" :description="formatDate(order.assignTime)" />
          <el-step title="接受任务" :description="order.acceptTime ? formatDate(order.acceptTime) : ''" />
          <el-step title="开始工作" :description="order.startTime ? formatDate(order.startTime) : ''" />
          <el-step title="完成工作" :description="order.actualCompletionTime ? formatDate(order.actualCompletionTime) : ''" />
        </el-steps>

        <div v-if="canUpdateStatus" class="progress-actions">
          <el-button
            v-if="order.status === 'assigned'"
            type="primary"
            @click="updateOrderStatus('accepted')"
            :loading="updating"
          >
            <el-icon><Check /></el-icon>
            接受任务
          </el-button>

          <el-button
            v-if="order.status === 'accepted'"
            type="success"
            @click="updateOrderStatus('in_progress')"
            :loading="updating"
          >
            <el-icon><Tools /></el-icon>
            开始工作
          </el-button>

          <el-button
            v-if="order.status === 'in_progress'"
            type="warning"
            @click="showCompleteDialog"
            :loading="updating"
          >
            <el-icon><CircleCheck /></el-icon>
            完成工作
          </el-button>
        </div>
      </el-card>

      <!-- 技师团队卡片 -->
      <el-card v-if="order.assignedTechnicians?.length" class="team-card">
        <template #header>
          <span>技师团队</span>
        </template>

        <div class="technician-list">
          <div
            v-for="technician in order.assignedTechnicians"
            :key="technician.technicianId"
            class="technician-item"
          >
            <el-avatar :size="40">{{ technician.realName?.charAt(0) || 'T' }}</el-avatar>
            <div class="technician-info">
              <div class="technician-name">{{ technician.realName }}</div>
              <div class="technician-specialty">{{ getSpecialtyName(technician.specialty) }}</div>
              <div class="technician-contact">{{ technician.phone }}</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 材料使用记录卡片 -->
      <MaterialUsageCard
        v-if="order.materialUsages"
        :material-usages="order.materialUsages"
        class="material-usage-section"
      />

      <!-- 费用信息卡片 -->
      <el-card class="cost-card">
        <template #header>
          <span>费用信息</span>
        </template>

        <div class="cost-breakdown">
          <div class="cost-item">
            <span class="cost-label">工时费</span>
            <span class="cost-value">{{ formatCost(order.totalLaborCost, order.status) }}</span>
          </div>
          <div class="cost-item">
            <span class="cost-label">材料费</span>
            <span class="cost-value">{{ formatCost(order.totalMaterialCost, order.status) }}</span>
          </div>
          <div class="cost-divider"></div>
          <div class="cost-item total">
            <span class="cost-label">总费用</span>
            <span class="cost-value">{{ formatCost(order.totalCost, order.status) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 工作记录卡片 -->
      <el-card v-if="order.workResult || order.status === 'completed'" class="work-record-card">
        <template #header>
          <span>工作记录</span>
        </template>

        <div class="work-record">
          <div v-if="order.workingHours" class="record-item">
            <span class="record-label">实际工时:</span>
            <span class="record-value">{{ order.workingHours }}小时</span>
          </div>
          <div v-if="order.workResult" class="record-item">
            <span class="record-label">维修结果:</span>
            <div class="record-content">{{ order.workResult }}</div>
          </div>
          <div v-if="order.actualCompletionTime" class="record-item">
            <span class="record-label">完成时间:</span>
            <span class="record-value">{{ formatDate(order.actualCompletionTime) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 催单信息卡片 -->
      <el-card v-if="order.urgentRequests && order.urgentRequests.length > 0" class="urgent-card">
        <template #header>
          <div class="card-header">
            <span>用户催单</span>
            <el-tag type="danger" size="small">
              <el-icon><Bell /></el-icon>
              {{ order.urgentRequests.length }}条催单
            </el-tag>
          </div>
        </template>

        <div class="urgent-list">
          <div
            v-for="urgent in order.urgentRequests"
            :key="urgent.urgentId"
            class="urgent-item"
          >
            <div class="urgent-header">
              <el-icon class="urgent-icon"><Bell /></el-icon>
              <span class="urgent-time">{{ formatDate(urgent.urgentTime) }}</span>
            </div>
            <div class="urgent-content">
              <p class="urgent-reason">{{ urgent.reason }}</p>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 用户评价卡片 -->
      <el-card v-if="order.feedback" class="rating-card">
        <template #header>
          <span>用户评价</span>
        </template>

        <div class="rating-content">
          <el-rate
            v-model="order.feedback.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
          <div v-if="order.feedback.comment" class="rating-comment">
            {{ order.feedback.comment }}
          </div>
          <div class="rating-time">
            评价时间: {{ formatDate(order.feedback.feedbackTime) }}
          </div>
        </div>
      </el-card>
    </div>

    <div v-else class="error-container">
      <el-empty description="工单不存在或已被删除" />
    </div>

    <!-- 完成工作对话框 -->
    <el-dialog
      v-model="completeDialog.visible"
      title="完成工作"
      width="500px"
      @close="resetCompleteForm"
    >
      <el-form
        ref="completeFormRef"
        :model="completeForm"
        :rules="completeRules"
        label-width="100px"
      >
        <el-form-item label="实际工时" prop="workingHours">
          <el-input-number
            v-model="completeForm.workingHours"
            :min="0.5"
            :step="0.5"
            :precision="1"
            placeholder="请输入实际工时"
            style="width: 100%"
          />
          <div class="form-tip">单位：小时</div>
        </el-form-item>

        <el-form-item label="维修结果" prop="workResult">
          <el-input
            v-model="completeForm.workResult"
            type="textarea"
            :rows="4"
            placeholder="请描述维修过程和结果"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="completeDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="updating"
          @click="submitCompleteForm"
        >
          确定完成
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft, Check, Tools, CircleCheck, Bell
} from '@element-plus/icons-vue'
import { orderAPI } from '@/api/order'
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'
import MaterialUsageCard from '@/components/MaterialUsageCard.vue'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const authStore = useAuthStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)
const canUpdateStatus = computed(() => {
  if (!order.value) return false
  const currentTechnicianId = authStore.user?.technicianId
  return order.value.assignedTechnicians?.some(tech => tech.technicianId === currentTechnicianId)
})

// 响应式数据
const loading = ref(false)
const updating = ref(false)
const order = ref(null)

// 表单引用
const completeFormRef = ref()

// 完成对话框
const completeDialog = reactive({
  visible: false
})

// 完成表单
const completeForm = reactive({
  workingHours: null,
  workResult: ''
})

// 完成表单验证规则
const completeRules = {
  workingHours: [
    { required: true, message: '请输入实际工时', trigger: 'blur' },
    { type: 'number', min: 0.5, message: '工时必须大于0.5小时', trigger: 'blur' }
  ],
  workResult: [
    { required: true, message: '请输入维修结果', trigger: 'blur' },
    { min: 10, message: '维修结果至少10个字符', trigger: 'blur' }
  ]
}

// 工种映射
const specialtyMap = {
  engine: { name: '发动机维修', type: 'primary' },
  transmission: { name: '变速箱维修', type: 'success' },
  brake: { name: '制动系统维修', type: 'warning' },
  electrical: { name: '电气系统维修', type: 'danger' },
  hvac: { name: '空调系统维修', type: 'info' },
  chassis: { name: '底盘维修', type: '' },
  body: { name: '车身维修', type: 'primary' },
  tire: { name: '轮胎维修', type: 'success' }
}

// 获取工单详情
const fetchOrderDetail = async () => {
  try {
    loading.value = true
    const orderId = route.params.id

    if (!orderId) {
      ElMessage.error('工单ID不存在')
      router.push('/technician/tasks')
      return
    }

    const response = await orderAPI.getById(orderId)
    order.value = response.data
  } catch (error) {
    console.error('Failed to fetch order detail:', error)
    ElMessage.error('获取工单详情失败')
    router.push('/technician/tasks')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 更新工单状态
const updateOrderStatus = async (status) => {
  try {
    updating.value = true

    if (status === 'accepted') {
      await orderAPI.accept(order.value.orderId)
      ElMessage.success('任务接受成功')
    } else if (status === 'in_progress') {
      await orderAPI.start(order.value.orderId)
      ElMessage.success('工作开始成功')
    } else {
      ElMessage.error('不支持的状态更新')
      return
    }

    // 重新获取工单详情
    await fetchOrderDetail()
  } catch (error) {
    console.error('Failed to update order status:', error)
    ElMessage.error('状态更新失败')
  } finally {
    updating.value = false
  }
}

// 显示完成对话框
const showCompleteDialog = () => {
  completeDialog.visible = true

  // 预填充预计工时
  if (order.value?.faultType?.estimatedHours) {
    completeForm.workingHours = order.value.faultType.estimatedHours
  }
}

// 重置完成表单
const resetCompleteForm = () => {
  Object.assign(completeForm, {
    workingHours: null,
    workResult: ''
  })

  // 清除表单验证
  if (completeFormRef.value) {
    completeFormRef.value.clearValidate()
  }
}

// 提交完成表单
const submitCompleteForm = async () => {
  try {
    // 表单验证
    if (!completeFormRef.value) return
    await completeFormRef.value.validate()

    updating.value = true

    // 使用技师专用的完成工单API
    await orderAPI.complete(order.value.orderId, {
      workingHours: completeForm.workingHours,
      workResult: completeForm.workResult
    })

    ElMessage.success('工单完成成功')
    completeDialog.visible = false

    // 重新获取工单详情
    await fetchOrderDetail()
  } catch (error) {
    console.error('Failed to complete order:', error)
    ElMessage.error('完成工单失败')
  } finally {
    updating.value = false
  }
}

// 获取步骤激活状态
const getStepActive = (status) => {
  const stepMap = {
    assigned: 0,
    accepted: 1,
    in_progress: 2,
    completed: 3
  }
  return stepMap[status] || 0
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    assigned: 'warning',
    accepted: 'primary',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    assigned: '已分配',
    accepted: '已接受',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取紧急程度类型
const getUrgencyType = (urgency) => {
  const urgencyMap = {
    low: 'info',
    normal: '',
    high: 'warning',
    urgent: 'danger'
  }
  return urgencyMap[urgency] || ''
}

// 获取紧急程度文本
const getUrgencyText = (urgency) => {
  const urgencyMap = {
    low: '低',
    normal: '中',
    high: '高',
    urgent: '紧急'
  }
  return urgencyMap[urgency] || urgency
}

// 获取工种名称
const getSpecialtyName = (specialty) => {
  return specialtyMap[specialty]?.name || specialty
}

// 格式化费用显示
const formatCost = (cost, status) => {
  // 如果订单未完成，显示"待结算"
  if (status !== 'completed') {
    return '待结算'
  }
  // 如果订单已完成，显示实际费用
  const numericCost = parseFloat(cost) || 0
  return `¥${numericCost.toFixed(2)}`
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.task-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.back-button {
  flex-shrink: 0;
}

.header-info {
  flex: 1;
}

.header-info h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.loading-container {
  padding: 40px 0;
}

.task-content {
  display: grid;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card,
.progress-card,
.team-card,
.material-usage-section,
.cost-card,
.work-record-card,
.rating-card {
  margin-bottom: 0;
}

/* 工作进度 */
.progress-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 技师团队 */
.technician-list {
  display: grid;
  gap: 16px;
}

.technician-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  gap: 16px;
}

.technician-info {
  flex: 1;
}

.technician-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.technician-specialty {
  font-size: 14px;
  color: #606266;
  margin-bottom: 2px;
}

.technician-contact {
  font-size: 12px;
  color: #909399;
}



/* 费用信息 */
.cost-breakdown {
  display: grid;
  gap: 12px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.cost-item.total {
  border-top: 2px solid #e4e7ed;
  padding-top: 16px;
  font-weight: 600;
  font-size: 16px;
}

.cost-label {
  color: #606266;
}

.cost-value {
  color: #303133;
  font-weight: 500;
}

.cost-item.total .cost-value {
  color: #f56c6c;
  font-size: 18px;
}

.cost-divider {
  height: 1px;
  background: #e4e7ed;
  margin: 8px 0;
}

/* 用户评价 */
.rating-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rating-comment {
  color: #606266;
  line-height: 1.6;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.rating-time {
  font-size: 12px;
  color: #909399;
}

/* 工作记录 */
.work-record {
  display: grid;
  gap: 16px;
}

.record-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.record-label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.record-value {
  color: #303133;
}

.record-content {
  color: #303133;
  line-height: 1.6;
  flex: 1;
}

/* 催单信息 */
.urgent-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.urgent-item {
  padding: 16px;
  background: #fef0f0;
  border-radius: 8px;
  border-left: 4px solid #f56c6c;
}

.urgent-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.urgent-icon {
  color: #f56c6c;
  font-size: 16px;
}

.urgent-time {
  font-size: 14px;
  color: #909399;
  flex: 1;
}

.urgent-content {
  margin-left: 24px;
}

.urgent-reason {
  color: #303133;
  line-height: 1.6;
  margin: 0;
  font-weight: 500;
}

/* 用户评价 */
.rating-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.rating-comment {
  margin-top: 12px;
  color: #606266;
  line-height: 1.6;
}

/* 错误状态 */
.error-container {
  padding: 60px 0;
  text-align: center;
}

/* 表单提示 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-detail {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-info h1 {
    font-size: 20px;
  }

  .progress-actions {
    flex-direction: column;
  }

  .progress-actions .el-button {
    width: 100%;
  }

  .technician-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .cost-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .record-item {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .task-detail {
    padding: 12px;
  }

  .header-info h1 {
    font-size: 18px;
  }

  .technician-item {
    padding: 12px;
  }
}
</style>