package com.example.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "technicians")
public class Technician extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "technician_id")
    private Long technicianId;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Column(name = "username", unique = true, nullable = false, length = 50)
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, message = "密码长度至少6个字符")
    @Column(name = "password", nullable = false)
    private String password;

    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 100, message = "真实姓名长度不能超过100个字符")
    @Column(name = "real_name", nullable = false, length = 100)
    private String realName;

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Column(name = "phone", nullable = false, length = 11)
    private String phone;

    @Email(message = "邮箱格式不正确")
    @Column(name = "email", length = 100)
    private String email;

    @NotNull(message = "工种不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "specialty", nullable = false, length = 20)
    private Specialty specialty;

    @NotNull(message = "时薪不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "时薪必须大于0")
    @Column(name = "hourly_rate", nullable = false, precision = 10, scale = 2)
    private BigDecimal hourlyRate;

    @NotNull(message = "入职日期不能为空")
    @Column(name = "hire_date", nullable = false)
    private LocalDate hireDate;

    @Column(name = "status", nullable = false)
    private Integer status = 1; // 1: 正常, 0: 禁用

    @Column(name = "workload", nullable = false)
    private Integer workload = 0; // 当前工作负载（分配的工单数）

    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO; // 平均评分

    // 一对多关系：技师的工单分配记录
    @OneToMany(mappedBy = "technician", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderTechnicianAssignment> orderAssignments = new ArrayList<>();

    // 一对多关系：技师的工时费记录
    @OneToMany(mappedBy = "technician", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LaborPayment> laborPayments = new ArrayList<>();

    // 枚举：工种
    public enum Specialty {
        ENGINE("机修"),
        TRANSMISSION("变速箱工"),
        BRAKE("制动工"),
        ELECTRICAL("电工"),
        HVAC("空调工"),
        CHASSIS("底盘工"),
        BODY("车身维修工"),
        TIRE("轮胎工");

        private final String displayName;

        Specialty(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    // 构造函数
    public Technician() {}

    public Technician(String username, String password, String realName, String phone,
                     Specialty specialty, BigDecimal hourlyRate, LocalDate hireDate) {
        this.username = username;
        this.password = password;
        this.realName = realName;
        this.phone = phone;
        this.specialty = specialty;
        this.hourlyRate = hourlyRate;
        this.hireDate = hireDate;
    }

    // Getters and Setters
    public Long getTechnicianId() {
        return technicianId;
    }

    public void setTechnicianId(Long technicianId) {
        this.technicianId = technicianId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Specialty getSpecialty() {
        return specialty;
    }

    public void setSpecialty(Specialty specialty) {
        this.specialty = specialty;
    }

    public BigDecimal getHourlyRate() {
        return hourlyRate;
    }

    public void setHourlyRate(BigDecimal hourlyRate) {
        this.hourlyRate = hourlyRate;
    }

    public LocalDate getHireDate() {
        return hireDate;
    }

    public void setHireDate(LocalDate hireDate) {
        this.hireDate = hireDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getWorkload() {
        return workload;
    }

    public void setWorkload(Integer workload) {
        this.workload = workload;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public List<OrderTechnicianAssignment> getOrderAssignments() {
        return orderAssignments;
    }

    public void setOrderAssignments(List<OrderTechnicianAssignment> orderAssignments) {
        this.orderAssignments = orderAssignments;
    }

    // 便利方法：获取已分配的工单列表
    public List<RepairOrder> getAssignedOrders() {
        return orderAssignments.stream()
                .map(OrderTechnicianAssignment::getRepairOrder)
                .collect(java.util.stream.Collectors.toList());
    }

    // 便利方法：获取已同意的工单列表
    public List<RepairOrder> getAcceptedOrders() {
        return orderAssignments.stream()
                .filter(assignment -> assignment.getAgreementStatus() == OrderTechnicianAssignment.AgreementStatus.ACCEPTED)
                .map(OrderTechnicianAssignment::getRepairOrder)
                .collect(java.util.stream.Collectors.toList());
    }

    public List<LaborPayment> getLaborPayments() {
        return laborPayments;
    }

    public void setLaborPayments(List<LaborPayment> laborPayments) {
        this.laborPayments = laborPayments;
    }
}
