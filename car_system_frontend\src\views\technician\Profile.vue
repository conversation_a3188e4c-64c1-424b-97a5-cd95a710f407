<template>
  <div class="technician-profile">
    <div class="page-header">
      <h1>个人资料</h1>
      <p>管理您的个人信息和工作设置</p>
    </div>

    <div class="profile-content">
      <!-- 基本信息卡片 -->
      <el-card class="profile-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" @click="showEditDialog">
              <el-icon><Edit /></el-icon>
              编辑基本信息
            </el-button>
          </div>
        </template>

        <div class="profile-info">
          <div class="avatar-section">
            <el-avatar :size="80" class="profile-avatar">
              {{ profile.realName?.charAt(0) || 'T' }}
            </el-avatar>
            <div class="avatar-info">
              <h3>{{ profile.realName }}</h3>
              <p>@{{ profile.username }}</p>
              <el-tag :type="getSpecialtyType(profile.specialty)" size="small">
                {{ getSpecialtyName(profile.specialty) }}
              </el-tag>
            </div>
          </div>

          <el-descriptions :column="isMobile ? 1 : 2" border>
            <el-descriptions-item label="真实姓名">
              {{ profile.realName }}
            </el-descriptions-item>
            <el-descriptions-item label="用户名">
              {{ profile.username }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              {{ profile.phone }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ profile.email }}
            </el-descriptions-item>
            <el-descriptions-item label="工种">
              <el-tag :type="getSpecialtyType(profile.specialty)" size="small">
                {{ getSpecialtyName(profile.specialty) }}
              </el-tag>
              <el-text type="info" size="small" style="margin-left: 8px;">
                (工种由管理员设置，不可修改)
              </el-text>
            </el-descriptions-item>
            <el-descriptions-item label="时薪">
              ¥{{ profile.hourlyRate }}/小时
              <el-text type="info" size="small" style="margin-left: 8px;">
                (时薪由管理员设置，不可修改)
              </el-text>
            </el-descriptions-item>
            <el-descriptions-item label="入职时间">
              {{ formatDate(profile.hireDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="评分">
              <el-rate
                v-model="profile.rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>




    </div>

    <!-- 编辑资料对话框 -->
    <el-dialog
      v-model="editDialog.visible"
      title="编辑基本信息"
      width="600px"
      @close="resetEditForm"
    >
      <el-alert
        title="提示"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;"
      >
        您只能修改基本联系信息，工种和时薪由管理员统一管理。
      </el-alert>

      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="editForm.realName"
            placeholder="请输入真实姓名"
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="editForm.phone"
            placeholder="请输入手机号"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="editForm.email"
            placeholder="请输入邮箱地址"
          />
        </el-form-item>


      </el-form>

      <template #footer>
        <el-button @click="editDialog.visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitEditForm"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Edit
} from '@element-plus/icons-vue'
import { technicianAPI } from '@/api/technician'
import { useAppStore } from '@/stores/app'
import dayjs from 'dayjs'

const appStore = useAppStore()

// 计算属性
const isMobile = computed(() => appStore.isMobile)

// 响应式数据
const submitting = ref(false)
const profile = ref({
  realName: '',
  username: '',
  phone: '',
  email: '',
  specialty: '',
  hourlyRate: 0,
  hireDate: '',
  rating: 0
})



// 表单引用
const editFormRef = ref()

// 编辑对话框
const editDialog = reactive({
  visible: false
})

// 编辑表单
const editForm = reactive({
  realName: '',
  phone: '',
  email: ''
})

// 编辑表单验证规则
const editRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}



// 工种映射
const specialtyMap = {
  engine: { name: '发动机维修', type: 'primary' },
  transmission: { name: '变速箱维修', type: 'success' },
  brake: { name: '制动系统维修', type: 'warning' },
  electrical: { name: '电气系统维修', type: 'danger' },
  hvac: { name: '空调系统维修', type: 'info' },
  chassis: { name: '底盘维修', type: 'info' },
  body: { name: '车身维修', type: 'primary' },
  tire: { name: '轮胎维修', type: 'success' }
}

// 获取个人资料
const fetchProfile = async () => {
  try {
    const response = await technicianAPI.getCurrentTechnician()
    profile.value = response.data.data || response.data || {}
  } catch (error) {
    console.error('Failed to fetch profile:', error)
    ElMessage.error('获取个人资料失败')
  }
}



// 显示编辑对话框
const showEditDialog = () => {
  editDialog.visible = true

  // 填充表单数据
  Object.assign(editForm, {
    realName: profile.value.realName,
    phone: profile.value.phone,
    email: profile.value.email
  })
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editForm, {
    realName: '',
    phone: '',
    email: ''
  })

  // 清除表单验证
  if (editFormRef.value) {
    editFormRef.value.clearValidate()
  }
}

// 提交编辑表单
const submitEditForm = async () => {
  try {
    // 表单验证
    if (!editFormRef.value) return
    await editFormRef.value.validate()

    submitting.value = true

    await technicianAPI.updateCurrentTechnician(editForm)
    ElMessage.success('基本信息更新成功')

    editDialog.visible = false
    fetchProfile()
  } catch (error) {
    console.error('Failed to update profile:', error)
    ElMessage.error('更新基本信息失败')
  } finally {
    submitting.value = false
  }
}

// 获取工种名称
const getSpecialtyName = (specialty) => {
  return specialtyMap[specialty]?.name || specialty
}

// 获取工种类型
const getSpecialtyType = (specialty) => {
  return specialtyMap[specialty]?.type || ''
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProfile()
})
</script>

<style scoped>
.technician-profile {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.profile-content {
  display: grid;
  gap: 20px;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
}

.profile-avatar {
  margin-right: 20px;
  font-size: 32px;
  font-weight: 600;
}

.avatar-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.avatar-info p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.stats-card {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.completed {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.stat-icon.progress {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
}

.stat-icon.hours {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.earnings {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .technician-profile {
    padding: 16px;
  }

  .avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .profile-avatar {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-item {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .technician-profile {
    padding: 12px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
